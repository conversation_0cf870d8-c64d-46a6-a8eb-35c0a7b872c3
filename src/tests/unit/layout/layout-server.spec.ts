/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { load } from '../../../routes/+layout.server';
import { createMockSupabaseClient } from '../../mocks/supabase';
import { ORG_COOKIE_NAME } from '$lib/current-org.svelte';

// Mock the flash message loader
vi.mock('sveltekit-flash-message/server', () => ({
	loadFlash: vi.fn((fn) => fn),
}));

describe('Layout Server Load', () => {
	let mockSupabase: ReturnType<typeof createMockSupabaseClient>;
	let mockCookies: {
		get: ReturnType<typeof vi.fn>;
		getAll: ReturnType<typeof vi.fn>;
		delete: ReturnType<typeof vi.fn>;
		set: ReturnType<typeof vi.fn>;
	};
	let mockLocals: {
		supabase: ReturnType<typeof createMockSupabaseClient>;
		user: { id: string } | null;
		session: unknown;
		orgId: string | null;
	};

	beforeEach(() => {
		vi.clearAllMocks();

		mockSupabase = createMockSupabaseClient();
		mockCookies = {
			get: vi.fn(),
			getAll: vi.fn().mockReturnValue([]),
			delete: vi.fn(),
			set: vi.fn(),
		};

		mockLocals = {
			supabase: mockSupabase,
			user: null,
			session: null,
			orgId: null,
		};
	});

	describe('Organization verification', () => {
		it('should not verify orgId when user is not authenticated', async () => {
			// Setup: no user, but orgId exists
			mockLocals.user = null;
			mockLocals.orgId = 'some-org-id';

			const mockEvent = {
				locals: mockLocals,
				depends: vi.fn(),
				cookies: mockCookies,
			};

			await load(mockEvent);

			// Should not call the RPC function when user is not authenticated
			expect(mockSupabase.rpc).not.toHaveBeenCalled();
			expect(mockCookies.delete).not.toHaveBeenCalled();
		});

		it('should not verify orgId when orgId is null', async () => {
			// Setup: user exists but no orgId
			mockLocals.user = { id: 'user-123' };
			mockLocals.orgId = null;

			const mockEvent = {
				locals: mockLocals,
				depends: vi.fn(),
				cookies: mockCookies,
			};

			await load(mockEvent);

			// Should not call the RPC function when orgId is null
			expect(mockSupabase.rpc).not.toHaveBeenCalled();
			expect(mockCookies.delete).not.toHaveBeenCalled();
		});

		it('should verify orgId and keep cookie when user has access', async () => {
			// Setup: user exists and has access to organization
			mockLocals.user = { id: 'user-123' };
			mockLocals.orgId = 'org-456';

			// Mock successful access check
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: true,
				error: null,
			});

			const mockEvent = {
				locals: mockLocals,
				depends: vi.fn(),
				cookies: mockCookies,
			};

			await load(mockEvent);

			// Should call the RPC function to verify access
			expect(mockSupabase.rpc).toHaveBeenCalledWith('current_user_has_entity_access', {
				entity_type_param: 'organization',
				entity_id_param: 'org-456',
			});

			// Should not delete the cookie since user has access
			expect(mockCookies.delete).not.toHaveBeenCalled();
			expect(mockLocals.orgId).toBe('org-456');
		});

		it('should clear cookie when user no longer has access', async () => {
			// Setup: user exists but no longer has access to organization
			mockLocals.user = { id: 'user-123' };
			mockLocals.orgId = 'org-456';

			// Mock failed access check
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: false,
				error: null,
			});

			const mockEvent = {
				locals: mockLocals,
				depends: vi.fn(),
				cookies: mockCookies,
			};

			await load(mockEvent);

			// Should call the RPC function to verify access
			expect(mockSupabase.rpc).toHaveBeenCalledWith('current_user_has_entity_access', {
				entity_type_param: 'organization',
				entity_id_param: 'org-456',
			});

			// Should delete the cookie since user no longer has access
			expect(mockCookies.delete).toHaveBeenCalledWith(ORG_COOKIE_NAME, { path: '/' });
			expect(mockLocals.orgId).toBe(null);
		});

		it('should clear cookie when RPC call returns an error', async () => {
			// Setup: user exists but RPC call fails
			mockLocals.user = { id: 'user-123' };
			mockLocals.orgId = 'org-456';

			// Mock RPC error
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: null,
				error: { message: 'Database error' },
			});

			const mockEvent = {
				locals: mockLocals,
				depends: vi.fn(),
				cookies: mockCookies,
			};

			await load(mockEvent);

			// Should call the RPC function to verify access
			expect(mockSupabase.rpc).toHaveBeenCalledWith('current_user_has_entity_access', {
				entity_type_param: 'organization',
				entity_id_param: 'org-456',
			});

			// Should delete the cookie since there was an error
			expect(mockCookies.delete).toHaveBeenCalledWith(ORG_COOKIE_NAME, { path: '/' });
			expect(mockLocals.orgId).toBe(null);
		});
	});

	describe('Data loading', () => {
		it('should load sidebar clients and other data correctly', async () => {
			// Setup basic authenticated user
			mockLocals.user = { id: 'user-123' };
			mockLocals.session = { user: { id: 'user-123' } };

			// Mock profile data
			const mockProfile = {
				user_id: 'user-123',
				full_name: 'Test User',
				email: '<EMAIL>',
			};

			// Mock clients data
			const mockClients = [
				{ client_id: 'client-1', name: 'Client 1', organization: { name: 'Org 1' } },
				{ client_id: 'client-2', name: 'Client 2', organization: { name: 'Org 2' } },
			];

			// Setup Supabase mocks
			const mockFrom = vi.fn().mockImplementation((table) => {
				if (table === 'profile') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
					};
				}
				if (table === 'client') {
					return {
						select: vi.fn().mockReturnThis(),
						order: vi.fn().mockResolvedValue({ data: mockClients, error: null }),
					};
				}
				return {
					select: vi.fn().mockReturnThis(),
					eq: vi.fn().mockReturnThis(),
					order: vi.fn().mockReturnThis(),
				};
			});

			mockSupabase.from = mockFrom;

			// Mock sidebar cookie
			mockCookies.get.mockImplementation((name) => {
				if (name === 'sidebar:state') return 'true';
				return undefined;
			});

			const mockEvent = {
				locals: mockLocals,
				depends: vi.fn(),
				cookies: mockCookies,
			};

			const result = await load(mockEvent);

			// Verify the result contains expected data
			expect(result).toEqual({
				session: mockLocals.session,
				user: mockLocals.user,
				profile: mockProfile,
				cookies: [],
				sidebarClients: mockClients,
				sidebarOpen: true,
			});

			// Verify dependencies were called
			expect(mockEvent.depends).toHaveBeenCalledWith('sidebar:clients');
		});
	});
});
