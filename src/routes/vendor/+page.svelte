<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { Badge } from '$lib/components/ui/badge';
	import type { VendorListItem } from '$lib/schemas/vendor';

	const { data }: { data: PageData } = $props();
	const { vendors, entityType, entityInfo } = data;

	function getEntityDisplayName() {
		if (!entityInfo) return 'Unknown';

		if (entityType === 'organization') {
			return entityInfo.name;
		} else if (entityType === 'client') {
			const clientInfo = entityInfo as { name: string; organization?: { name: string } };
			return `${clientInfo.organization?.name} > ${clientInfo.name}`;
		} else if (entityType === 'project') {
			const projectInfo = entityInfo as {
				name: string;
				client?: { name: string; organization?: { name: string } };
			};
			return `${projectInfo.client?.organization?.name} > ${projectInfo.client?.name} > ${projectInfo.name}`;
		}
		return 'Unknown';
	}

	function getAccessLevelBadgeVariant(accessLevel: VendorListItem['access_level']) {
		switch (accessLevel) {
			case 'organization':
				return 'default';
			case 'client':
				return 'secondary';
			case 'project':
				return 'outline';
			default:
				return 'outline';
		}
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-semibold">Vendors</h1>
			<p class="text-muted-foreground mt-1">
				Viewing vendors for: {getEntityDisplayName()}
			</p>
		</div>
		<Button href="/vendor/new" class="gap-2">
			<PlusIcon class="size-4" />
			New Vendor
		</Button>
	</div>

	{#if vendors.length === 0}
		<div class="rounded-lg border border-dashed p-8 text-center">
			<p class="text-muted-foreground">
				No vendors found. Create your first vendor to get started.
			</p>
		</div>
	{:else}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead class="w-64">Name</TableHead>
						<TableHead class="w-32">Type</TableHead>
						<TableHead class="w-48">Contact</TableHead>
						<TableHead class="w-32">Access Level</TableHead>
						<TableHead class="w-24">Status</TableHead>
						<TableHead class="w-20 pr-4 text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each vendors as vendor (vendor.vendor_id)}
						<TableRow>
							<TableCell class="font-medium">
								<div>
									<div class="font-medium">{vendor.name}</div>
									{#if vendor.description}
										<div class="text-muted-foreground text-sm">{vendor.description}</div>
									{/if}
								</div>
							</TableCell>
							<TableCell>
								{vendor.vendor_type || 'Not specified'}
							</TableCell>
							<TableCell>
								<div class="text-sm">
									{#if vendor.contact_name}
										<div class="font-medium">{vendor.contact_name}</div>
									{/if}
									{#if vendor.contact_email}
										<div class="text-muted-foreground">{vendor.contact_email}</div>
									{/if}
									{#if vendor.contact_phone}
										<div class="text-muted-foreground">{vendor.contact_phone}</div>
									{/if}
									{#if !vendor.contact_name && !vendor.contact_email && !vendor.contact_phone}
										<span class="text-muted-foreground">No contact info</span>
									{/if}
								</div>
							</TableCell>
							<TableCell>
								<Badge variant={getAccessLevelBadgeVariant(vendor.access_level)}>
									{vendor.access_level}
								</Badge>
							</TableCell>
							<TableCell>
								<Badge variant={vendor.is_active ? 'default' : 'secondary'}>
									{vendor.is_active ? 'Active' : 'Inactive'}
								</Badge>
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="size-8 p-0">
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="size-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item>
											<a href="/vendor/{vendor.vendor_id}" class="flex w-full"> View Details </a>
										</DropdownMenu.Item>
										<DropdownMenu.Item>
											<a href="/vendor/{vendor.vendor_id}/edit" class="flex w-full"> Edit </a>
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{/if}
</div>
