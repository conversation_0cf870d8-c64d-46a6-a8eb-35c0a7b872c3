import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params }) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { vendor_id } = params;

	// Get the vendor data with related entity information
	const { data: vendor, error: vendorError } = await supabase
		.from('vendor')
		.select(
			`
			*,
			organization:org_id(org_id, name),
			client:client_id(client_id, name, organization(org_id, name)),
			project:project_id(project_id, name, client(client_id, name, organization(org_id, name)))
		`,
		)
		.eq('vendor_id', vendor_id)
		.single();

	if (vendorError || !vendor) {
		throw error(404, 'Vendor not found');
	}

	// Check if user has access to view this vendor
	// This will be enforced by RLS, but we can also check explicitly
	const entityId = vendor.org_id || vendor.client_id || vendor.project_id;
	if (!entityId) {
		throw error(400, 'Vendor must be associated with an organization, client, or project');
	}

	const { data: hasAccess } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: vendor.org_id ? 'organization' : vendor.client_id ? 'client' : 'project',
		entity_id_param: entityId,
	});

	if (!hasAccess || !hasAccess.find((v) => v.vendor_id === vendor_id)) {
		throw error(403, 'You do not have permission to view this vendor');
	}

	// Determine access level and entity info
	let accessLevel: 'organization' | 'client' | 'project';
	let entityInfo: unknown;

	if (vendor.org_id) {
		accessLevel = 'organization';
		entityInfo = vendor.organization;
	} else if (vendor.client_id) {
		accessLevel = 'client';
		entityInfo = vendor.client;
	} else {
		accessLevel = 'project';
		entityInfo = vendor.project;
	}

	return {
		vendor,
		accessLevel,
		entityInfo,
	};
};
