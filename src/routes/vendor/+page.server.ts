import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { requireUser } from '$lib/server/auth';

import type { VendorListItem } from '$lib/schemas/vendor';

export const load: PageServerLoad = async ({ locals, url }) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	// Get query parameters for filtering
	const entityType = url.searchParams.get('entity_type') || 'organization';
	const entityId = url.searchParams.get('entity_id');

	// If no entity_id is provided, we need to determine the appropriate scope
	// For now, we'll default to organization level if user has access to any organization
	let actualEntityType = entityType as 'organization' | 'client' | 'project';
	let actualEntityId = entityId;

	if (!actualEntityId) {
		// Get user's organizations to default to the first one
		const { data: memberships } = await supabase
			.from('membership')
			.select('entity_id, entity_type')
			.eq('user_id', user.id)
			.eq('entity_type', 'organization')
			.limit(1);

		if (memberships && memberships.length > 0) {
			actualEntityType = 'organization';
			actualEntityId = memberships[0].entity_id;
		} else {
			// If no organization access, try client level
			const { data: clientMemberships } = await supabase
				.from('membership')
				.select('entity_id, entity_type')
				.eq('user_id', user.id)
				.eq('entity_type', 'client')
				.limit(1);

			if (clientMemberships && clientMemberships.length > 0) {
				actualEntityType = 'client';
				actualEntityId = clientMemberships[0].entity_id;
			} else {
				// Try project level as last resort
				const { data: projectMemberships } = await supabase
					.from('membership')
					.select('entity_id, entity_type')
					.eq('user_id', user.id)
					.eq('entity_type', 'project')
					.limit(1);

				if (projectMemberships && projectMemberships.length > 0) {
					actualEntityType = 'project';
					actualEntityId = projectMemberships[0].entity_id;
				} else {
					throw error(403, 'No access to any entities');
				}
			}
		}
	}

	// Call the RPC function to get accessible vendors
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: actualEntityType,
		entity_id_param: actualEntityId,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw error(500, 'Failed to fetch vendors');
	}

	// Get entity information for context
	let entityInfo = null;
	if (actualEntityType === 'organization') {
		const { data: org } = await supabase
			.from('organization')
			.select('name, org_id')
			.eq('org_id', actualEntityId)
			.single();
		entityInfo = org;
	} else if (actualEntityType === 'client') {
		const { data: client } = await supabase
			.from('client')
			.select('name, client_id, organization(name)')
			.eq('client_id', actualEntityId)
			.single();
		entityInfo = client;
	} else if (actualEntityType === 'project') {
		const { data: project } = await supabase
			.from('project')
			.select('name, project_id, client(name, organization(name))')
			.eq('project_id', actualEntityId)
			.single();
		entityInfo = project;
	}

	return {
		vendors: (vendors as VendorListItem[]) || [],
		entityType: actualEntityType,
		entityId: actualEntityId,
		entityInfo,
	};
};
