import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	vendorSchema,
	processVendorCreationHierarchy,
	type VendorCreationHierarchyItem,
} from '$lib/schemas/vendor';
import { requireUser } from '$lib/server/auth';
import { createVendor } from '$lib/components/forms/vendor/vendor_form_actions';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;

	// Get vendor creation hierarchy using the optimized RPC function
	const { data: hierarchyData, error: hierarchyError } = await supabase.rpc(
		'get_vendor_creation_hierarchy',
	);

	if (hierarchyError) {
		console.error('Error fetching vendor creation hierarchy:', hierarchyError);
		throw new Error('Failed to fetch vendor creation hierarchy');
	}

	// Process the hierarchy data into UI-friendly format
	const { organizations, clients, projects } = processVendorCreationHierarchy(
		(hierarchyData as VendorCreationHierarchyItem[]) || [],
	);

	const form = await superValidate(zod(vendorSchema));

	return {
		form,
		organizations,
		clients,
		projects,
	};
};

export const actions: Actions = {
	default: createVendor,
};
