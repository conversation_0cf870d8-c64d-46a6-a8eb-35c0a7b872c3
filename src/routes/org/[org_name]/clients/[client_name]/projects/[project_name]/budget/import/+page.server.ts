import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw new Error('Project not found');
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	if (!canEditProject) {
		throw new Error('You do not have permission to import budget data for this project');
	}

	return {
		project: projectData,
	};
};
