import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project data with client and organization information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(client_id, name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw new Error('Project not found');
	}

	// Fetch invoices using the RPC function
	const { data: invoices, error: invoicesError } = await supabase.rpc('get_accessible_invoices', {
		project_id_param: projectData.project_id,
	});

	if (invoicesError) {
		console.error('Error fetching invoices:', invoicesError);
		throw new Error('Failed to fetch invoices');
	}

	return {
		project: projectData,
		invoices: invoices || [],
	};
};
