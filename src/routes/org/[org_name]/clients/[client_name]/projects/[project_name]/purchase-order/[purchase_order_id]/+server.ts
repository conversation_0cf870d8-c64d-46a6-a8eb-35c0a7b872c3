import { error, json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';

export const DELETE: RequestHandler = async ({ params, locals }) => {
	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_name, purchase_order_id } = params;
	requireProject();

	// Verify the purchase order exists and belongs to the correct project
	const { data: purchaseOrder, error: fetchError } = await supabase
		.from('purchase_order')
		.select(
			`
			purchase_order_id,
			po_number,
			project!inner(
				project_id,
				name,
				client!inner(
					name,
					organization!inner(name)
				)
			)
		`,
		)
		.eq('purchase_order_id', purchase_order_id)
		.eq('project.client.organization.name', org_name)
		.eq('project.client.name', client_name)
		.eq('project.name', project_name)
		.single();

	if (fetchError || !purchaseOrder) {
		console.error('Error fetching purchase order for deletion:', fetchError);
		throw error(404, 'Purchase order not found');
	}

	// Delete the purchase order
	const { error: deleteError } = await supabase
		.from('purchase_order')
		.delete()
		.eq('purchase_order_id', purchase_order_id);

	if (deleteError) {
		console.error('Error deleting purchase order:', deleteError);
		throw error(500, 'Failed to delete purchase order');
	}

	return json({
		success: true,
		message: `Purchase order ${purchaseOrder.po_number} deleted successfully`,
	});
};
