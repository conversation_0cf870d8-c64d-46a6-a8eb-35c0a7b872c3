import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { invoiceSchema } from '$lib/schemas/invoice';
import { vendorSchema } from '$lib/schemas/vendor';
import {
	createInvoice,
	createInvoiceModal,
} from '$lib/components/forms/invoice/invoice_form_actions';
import { createVendor, createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, cookies }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project data with client and organization information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(client_id, name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Get vendors accessible for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		entity_type_param: 'project',
		entity_id_param: projectData.project_id,
		user_id_param: (await requireUser()).user.id,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw new Error('Failed to fetch vendors');
	}

	// Get purchase orders for this project
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_accessible_purchase_orders',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw new Error('Failed to fetch purchase orders');
	}

	// Initialize forms
	const form = await superValidate(zod(invoiceSchema));

	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newVendorForm,
		project: projectData,
		vendors: vendors || [],
		purchaseOrders: purchaseOrders || [],
	};
};

export const actions: Actions = {
	createInvoice: async (event) => {
		const result = await createInvoice(event);

		if (result && 'form' in result && result.form.valid) {
			const { org_name, client_name, project_name } = requireProject();
			return redirect(
				303,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}/actuals`,
				{ type: 'success', message: 'Invoice created successfully' },
				event.cookies,
			);
		}

		return result;
	},
	createInvoiceModal,
	createVendor,
	createVendorModal,
};
