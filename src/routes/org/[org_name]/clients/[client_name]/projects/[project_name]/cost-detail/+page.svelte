<script lang="ts">
	import type {
		CostDetailBudgetItem,
		CostDetailWorkPackageData,
		CostDetailPurchaseOrderData,
	} from '$lib/schemas/cost_detail';
	import { createGenericBudgetHierarchy } from '$lib/budget_utils';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { formatCurrency } from '$lib/schemas/purchase_order';
	import type { HierarchyNode } from 'd3-hierarchy';
	import type { WbsItemWithBudgetData } from '$lib/budget_utils';
	import { SvelteSet } from 'svelte/reactivity';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { page } from '$app/state';

	const { data } = $props();
	const { costDetailData, allWbsItems } = data;

	// State for hiding zero values
	let hideZeros = $state(true);

	// State for expanding/collapsing nodes
	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}

	// Create the hierarchical structure using the budget utils
	const costHierarchy = $derived.by(() => {
		if (!allWbsItems || !costDetailData) {
			return null;
		}
		return createGenericBudgetHierarchy(allWbsItems, costDetailData);
	});

	// Type for hierarchy node with cost detail data
	type CostDetailHierarchyNode = HierarchyNode<WbsItemWithBudgetData<CostDetailBudgetItem>>;

	// Check if a node has its own values (not considering children)
	const nodeHasOwnValues = (node: CostDetailHierarchyNode): boolean => {
		// Safety check for node and node.data
		if (!node || !node.data) return false;

		// Safely access budgetData
		const budgetData = node.data?.budgetData;

		// Show if has budget amount
		if (budgetData?.budget_amount && budgetData.budget_amount > 0) return true;

		// Show if has work packages
		if (budgetData?.work_packages && budgetData.work_packages.length > 0) return true;

		// Show if has purchase orders
		if (budgetData?.purchase_orders && budgetData.purchase_orders.length > 0) return true;

		return false;
	};

	// Type for display row
	type DisplayRow = {
		id: string;
		wbs_code: string;
		wbs_description: string;
		budget_amount: number | null;
		hierarchical_total: number | null; // Total including children from stratify
		is_hierarchical_total: boolean; // Whether this row shows a hierarchical total
		work_packages: CostDetailWorkPackageData[];
		purchase_orders: CostDetailPurchaseOrderData[];
		indentStyle: string;
		level: number;
		hasChildren: boolean;
		nodeId: string;
		// New fields for work package rows
		is_work_package_row: boolean; // Whether this is a work package row
		work_package: CostDetailWorkPackageData | null; // Single work package for work package rows
		work_package_purchase_orders: CostDetailPurchaseOrderData[]; // Purchase orders for this specific work package
	};

	// Recursive function to render hierarchy nodes with improved filtering and expand/collapse support
	function renderHierarchyRows(node: CostDetailHierarchyNode, level: number = 0): DisplayRow[] {
		// Safety check for node and node.data
		if (!node || !node.data) return [];

		const indentStyle = level > 0 ? `padding-left: ${level * 1.5}rem;` : '';
		// Safely access budgetData
		const budgetData = node.data?.budgetData;
		const workPackages = budgetData?.work_packages || [];
		const purchaseOrders = budgetData?.purchase_orders || [];
		const nodeId = node.data.wbs_library_item_id || `unknown-${level}`;
		const hasChildren = Boolean(node.children && node.children.length > 0);

		const rows: DisplayRow[] = [];

		// Check if this node is expanded (default to expanded if not in the set)
		const isExpanded = !expandedNodeIds.has(nodeId);

		// First, process children to get their rows (only if expanded)
		const childRows: DisplayRow[] = [];
		if (hasChildren && isExpanded) {
			for (const child of node.children!) {
				childRows.push(...renderHierarchyRows(child, level + 1));
			}
		}

		// Determine if this node should be shown:
		// - Always show if hideZeros is false
		// - Show if node has its own values
		// - Show if any children are being shown (meaning they have values)
		const hasOwnValues = nodeHasOwnValues(node);
		const hasChildrenWithValues = childRows.length > 0;
		const shouldShow = !hideZeros || hasOwnValues || hasChildrenWithValues || hasChildren;

		// If we should show this node, add it to the rows
		if (shouldShow) {
			// Determine if this should show hierarchical total or individual budget amount
			// Show hierarchical total for parent nodes that have children with values
			const hasChildrenWithBudgetValues =
				hasChildren &&
				childRows.some((row) => row.budget_amount !== null || row.hierarchical_total !== null);
			const isHierarchicalTotal =
				hasChildren && (hasChildrenWithBudgetValues || (node.value || 0) > 0);

			// Create the parent WBS row
			const parentRow: DisplayRow = {
				id: nodeId,
				wbs_code: budgetData?.wbs_code || node.data.code || '—',
				wbs_description: budgetData?.wbs_description || node.data.description || '—',
				budget_amount: budgetData?.budget_amount || null,
				hierarchical_total: isHierarchicalTotal ? node.value || null : null,
				is_hierarchical_total: isHierarchicalTotal,
				work_packages: workPackages,
				purchase_orders: purchaseOrders,
				indentStyle,
				level,
				hasChildren,
				nodeId,
				is_work_package_row: false,
				work_package: null,
				work_package_purchase_orders: [],
			};
			rows.push(parentRow);

			// Create individual work package rows if there are work packages
			if (workPackages.length > 0) {
				for (const workPackage of workPackages) {
					// Find purchase orders for this specific work package
					const workPackagePurchaseOrders = purchaseOrders.filter(
						(po) => po.purchase_order_id === workPackage.purchase_order_id,
					);

					const workPackageRow: DisplayRow = {
						id: `${nodeId}-wp-${workPackage.work_package_id}`,
						wbs_code: '', // Empty for work package rows
						wbs_description: '', // Empty for work package rows
						budget_amount: null, // Empty for work package rows
						hierarchical_total: null, // Empty for work package rows
						is_hierarchical_total: false,
						work_packages: [], // Empty for work package rows
						purchase_orders: [], // Empty for work package rows
						indentStyle:
							level > 0 ? `padding-left: ${(level + 1) * 1.5}rem;` : 'padding-left: 1.5rem;',
						level: level + 1,
						hasChildren: false,
						nodeId: `${nodeId}-wp-${workPackage.work_package_id}`,
						is_work_package_row: true,
						work_package: workPackage,
						work_package_purchase_orders: workPackagePurchaseOrders,
					};
					rows.push(workPackageRow);
				}
			}
		}

		// Add child rows (they've already been filtered and only included if expanded)
		rows.push(...childRows);

		return rows;
	}

	// Create display rows from hierarchy
	const displayRows = $derived.by((): DisplayRow[] => {
		if (!costHierarchy) return [];
		return renderHierarchyRows(costHierarchy as unknown as CostDetailHierarchyNode);
	});
</script>

<div class="container py-6">
	<div class="space-y-6">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">Cost Detail</h1>
				<p class="text-muted-foreground">
					Hierarchical view of WBS codes, budget amounts, work packages, and purchase orders
				</p>
			</div>

			<div class="flex items-center space-x-2">
				<Checkbox id="hide-zeros" bind:checked={hideZeros} />
				<Label for="hide-zeros">Hide zero values</Label>
			</div>
		</div>

		{#if !costDetailData || costDetailData.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No cost detail data found</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Add budget items, work packages, or purchase orders to see cost details
				</p>
			</div>
		{:else if displayRows.length === 0}
			<div class="py-12 text-center">
				<p class="text-muted-foreground text-lg">No items to display</p>
				<p class="text-muted-foreground mt-2 text-sm">
					Try unchecking "Hide zero values" to see all items
				</p>
			</div>
		{:else}
			<div class="rounded-md border">
				<Table class="w-full table-fixed">
					<TableHeader>
						<TableRow>
							<TableHead class="w-48">WBS Code</TableHead>
							<TableHead class="w-96">Description</TableHead>
							<TableHead class="w-48 pr-4 text-right">Budget Amount / Total</TableHead>
							<TableHead class="w-48">Work Packages</TableHead>
							<TableHead class="w-48">Purchase Orders</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each displayRows as row (row.id)}
							{#if row.is_work_package_row}
								<!-- Work Package Row -->
								<TableRow class="bg-muted/10 work-package-row">
									<TableCell class="font-medium"></TableCell>
									<TableCell class="text-muted-foreground"></TableCell>
									<TableCell class="pr-4 text-right"></TableCell>
									<TableCell class="text-muted-foreground">
										{#if row.work_package}
											<a
												href="/org/{encodeURIComponent(
													page.params.org_name,
												)}/clients/{encodeURIComponent(
													page.params.client_name,
												)}/projects/{encodeURIComponent(page.params.project_name)}/work-package/{row
													.work_package.work_package_id}"
												class="text-blue-600 hover:text-blue-800 hover:underline"
											>
												{row.work_package.name}{row.work_package.description?.length
													? ` (${row.work_package.description})`
													: ''}
											</a>
										{:else}
											—
										{/if}
									</TableCell>
									<TableCell class="text-muted-foreground">
										{#if row.work_package_purchase_orders.length > 0}
											{#each row.work_package_purchase_orders as po, index (po.purchase_order_id)}
												<a
													href="/org/{encodeURIComponent(
														page.params.org_name,
													)}/clients/{encodeURIComponent(
														page.params.client_name,
													)}/projects/{encodeURIComponent(
														page.params.project_name,
													)}/purchase-order/{po.purchase_order_id}/edit"
													class="text-blue-600 hover:text-blue-800 hover:underline"
												>
													{po.po_number}{po.vendor_name ? ` (${po.vendor_name})` : ''}
												</a>{index < row.work_package_purchase_orders.length - 1 ? ', ' : ''}
											{/each}
										{:else}
											—
										{/if}
									</TableCell>
								</TableRow>
							{:else}
								<!-- WBS Code Row -->
								<TableRow
									class={row.is_hierarchical_total
										? 'bg-muted/30 code-row font-medium'
										: 'code-row'}
								>
									<TableCell class="font-medium">
										<div class="flex items-center" style={row.indentStyle}>
											{#if row.hasChildren}
												{@const isExpanded = !expandedNodeIds.has(row.nodeId)}
												<button
													onclick={() => toggleNodeExpanded(row.nodeId)}
													aria-label={isExpanded ? 'Collapse' : 'Expand'}
													class="hover:bg-muted mr-2 rounded p-1"
												>
													{#if isExpanded}
														<CaretDownIcon class="size-4" />
													{:else}
														<CaretRightIcon class="size-4" />
													{/if}
												</button>
												<span class={row.is_hierarchical_total ? 'font-semibold' : ''}
													>{row.wbs_code}</span
												>
											{:else}
												<span class="ml-8">{row.wbs_code}</span>
											{/if}
										</div>
									</TableCell>
									<TableCell
										class={row.is_hierarchical_total ? 'text-foreground' : 'text-muted-foreground'}
									>
										<p
											class="w-full text-wrap {row.is_hierarchical_total ? 'font-medium' : ''}"
											style={row.indentStyle}
										>
											{row.wbs_description}
										</p>
									</TableCell>
									<TableCell class="pr-4 text-right">
										<div class="flex flex-col">
											{#if row.hierarchical_total}
												<span class="font-bold">
													{formatCurrency(row.hierarchical_total, 0)}
												</span>
											{/if}
											{#if row.budget_amount}
												<span class="text-muted-foreground pt-1 text-sm">
													{formatCurrency(row.budget_amount, 0)}
												</span>
											{/if}
											{#if !row.hierarchical_total && !row.budget_amount}
												—
											{/if}
										</div>
									</TableCell>
									<TableCell class="text-muted-foreground">
										{#if !row.work_packages}
											—
										{/if}
									</TableCell>
									<TableCell class="text-muted-foreground">
										{#if !row.work_packages}
											—
										{/if}
									</TableCell>
								</TableRow>
							{/if}
						{/each}
					</TableBody>
				</Table>
			</div>
		{/if}
	</div>
</div>
