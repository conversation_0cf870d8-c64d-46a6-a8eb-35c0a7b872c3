<script lang="ts">
	import type { PageData } from './$types';
	import NewPurchaseOrder from '$lib/components/forms/purchase_order/NewPurchaseOrder.svelte';

	const { data }: { data: PageData } = $props();
</script>

<div class="container mx-auto py-8">
	<NewPurchaseOrder
		data={{
			form: data.form,
			newVendorForm: data.newVendorForm,
			vendors: data.vendors,
			project: data.project,
		}}
	/>
</div>
