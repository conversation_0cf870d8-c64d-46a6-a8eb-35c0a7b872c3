import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, depends }) => {
	depends('project:purchase-orders');
	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Fetch the project data to get project_id
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('project_id, name, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw new Error('Project not found');
	}

	// Fetch purchase orders using the RPC function
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_accessible_purchase_orders',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw new Error('Failed to fetch purchase orders');
	}

	return {
		project: projectData,
		purchaseOrders: purchaseOrders || [],
	};
};
