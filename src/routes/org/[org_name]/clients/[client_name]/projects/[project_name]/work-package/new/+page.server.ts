import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { workPackageSchema } from '$lib/schemas/work_package';
import { purchaseOrderModalSchema } from '$lib/schemas/purchase_order';
import { vendorSchema } from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import { createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { createPurchaseOrderModal } from '$lib/components/forms/purchase_order/purchase_order_form_actions';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(client_id, name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw error(404, 'Project not found');
	}

	// Fetch purchase orders for selection
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_purchase_orders_for_work_package',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw error(500, 'Failed to fetch purchase orders');
	}

	// Fetch WBS library items for selection
	const { data: wbsItems, error: wbsItemsError } = await supabase.rpc(
		'get_wbs_items_for_work_package',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (wbsItemsError) {
		console.error('Error fetching WBS items:', wbsItemsError);
		throw error(500, 'Failed to fetch WBS items');
	}

	// Fetch accessible vendors for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: (await requireUser()).user.id,
		entity_type_param: 'project',
		entity_id_param: projectData.project_id,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw error(500, 'Failed to fetch vendors');
	}

	const form = await superValidate(zod(workPackageSchema));
	const newPurchaseOrderForm = await superValidate(zod(purchaseOrderModalSchema));
	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newPurchaseOrderForm,
		newVendorForm,
		project: projectData,
		purchaseOrders: purchaseOrders || [],
		wbsItems: wbsItems || [],
		vendors: vendors || [],
	};
};

export const actions: Actions = {
	createVendorModal,
	createPurchaseOrderModal,
	createWorkPackage: async ({ request, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject();

		const form = await superValidate(request, zod(workPackageSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project_id
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(client_id, name, organization(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Create the work package
		const { data: workPackage, error: workPackageError } = await supabase
			.from('work_package')
			.insert({
				...form.data,
				project_id: projectData.project_id,
			})
			.select('work_package_id, name')
			.single();

		if (workPackageError) {
			console.error('Error creating work package:', workPackageError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_name)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackage.name}" created successfully`,
			},
			cookies,
		);
	},
};
