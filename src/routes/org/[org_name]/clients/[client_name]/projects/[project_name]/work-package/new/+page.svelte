<script lang="ts">
	import type { PageData } from './$types';
	import * as Form from '$lib/components/ui/form';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import * as Dialog from '$lib/components/ui/dialog';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { workPackageSchema } from '$lib/schemas/work_package';
	import { toast } from 'svelte-sonner';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';
	import { cn } from '$lib/utils';
	import CheckIcon from '@lucide/svelte/icons/check';
	import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
	import NewPurchaseOrder, {
		type PurchaseOrderListItem,
	} from '$lib/components/forms/purchase_order/NewPurchaseOrder.svelte';
	import type { VendorListItem } from '$lib/schemas/vendor';
	const { data }: { data: PageData } = $props();
	const { project, purchaseOrders, wbsItems, vendors } = data;

	const form = superForm(data.form, {
		validators: zodClient(workPackageSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	// Create WBS options from flat list
	const flatWbsOptions = wbsItems.map((item) => ({
		value: item.wbs_library_item_id,
		label: `${'  '.repeat(item.level)}${item.code} - ${item.description}`,
	}));

	// Purchase order options
	let purchaseOrderList = $state([...purchaseOrders]);
	let purchaseOrderOptions = $derived(
		purchaseOrderList.map((po) => ({
			value: po.purchase_order_id,
			label: po.vendor_name ? `${po.po_number} - ${po.vendor_name}` : po.po_number,
		})),
	);

	// Proxy variables for nullable fields to work with Select components
	let purchaseOrderProxy = $state($formData.purchase_order_id || '');

	$effect(() => {
		$formData.purchase_order_id = purchaseOrderProxy || null;
	});

	// Purchase order creation state
	let purchaseOrderDialogOpen = $state(false);

	// Handle purchase order creation from modal
	function handlePurchaseOrderCreated(purchaseOrder: PurchaseOrderListItem | null) {
		if (purchaseOrder) {
			// Convert PurchaseOrderListItem to match the existing purchase order list format
			const purchaseOrderForList = {
				purchase_order_id: purchaseOrder.purchase_order_id,
				po_number: purchaseOrder.po_number,
				description: purchaseOrder.description || '',
				vendor_name: purchaseOrder.vendor_name || '',
			};
			// Add the new purchase order to the list
			purchaseOrderList = [
				...purchaseOrderList,
				purchaseOrderForList as (typeof purchaseOrderList)[0],
			];
			// Auto-select the new purchase order
			purchaseOrderProxy = purchaseOrder.purchase_order_id;
			// Close the dialog
			purchaseOrderDialogOpen = false;
		} else {
			// Just close the dialog (cancel was clicked)
			purchaseOrderDialogOpen = false;
		}
	}

	// Combobox state
	let wbsOpen = $state(false);
	let purchaseOrderOpen = $state(false);

	// Helper function to close combobox and refocus trigger
	function closeAndFocusTrigger(triggerId: string, setOpen: (value: boolean) => void) {
		setOpen(false);
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}

	// Generate unique IDs for triggers
	const wbsTriggerId = useId();
	const purchaseOrderTriggerId = useId();
</script>

<div class="container mx-auto max-w-2xl py-8">
	<div class="mb-8 space-y-4">
		<div class="space-y-2">
			<h1 class="text-3xl font-bold tracking-tight">Create Work Package</h1>
			<p class="text-muted-foreground">
				Create a new work package for {project.name}
			</p>
		</div>
	</div>

	<div class="rounded-lg border p-6 shadow-sm">
		<form method="POST" use:enhance action="?/createWorkPackage">
			<div class="space-y-6">
				<!-- Basic Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Basic Information</h3>

					<Form.Field {form} name="name">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Work Package Name <span class="text-red-500">*</span></Form.Label>
								<Input
									{...props}
									type="text"
									placeholder="Enter work package name"
									bind:value={$formData.name}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea
									{...props}
									placeholder="Enter work package description"
									bind:value={$formData.description}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- WBS Library Item Selection -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">WBS Library Item</h3>

					<Form.Field {form} name="wbs_library_item_id">
						<Popover.Root bind:open={wbsOpen}>
							<Form.Control id={wbsTriggerId}>
								{#snippet children({ props })}
									<Form.Label>WBS Library Item <span class="text-red-500">*</span></Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between',
											!$formData.wbs_library_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										{...props}
									>
										{flatWbsOptions.find((f) => f.value === $formData.wbs_library_item_id)?.label ??
											'Select a WBS library item'}
										<ChevronsUpDownIcon class="opacity-50" />
									</Popover.Trigger>
									<input hidden value={$formData.wbs_library_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content class="w-full p-0">
								<Command.Root>
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No WBS item found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										{#each flatWbsOptions as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$formData.wbs_library_item_id = option.value;
													closeAndFocusTrigger(wbsTriggerId, (value) => (wbsOpen = value));
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto',
														option.value !== $formData.wbs_library_item_id && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Relationships -->
				<div class="space-y-4">
					<h3 class="text-lg font-medium">Relationships</h3>

					<Form.Field {form} name="purchase_order_id">
						<Popover.Root bind:open={purchaseOrderOpen}>
							<Form.Control id={purchaseOrderTriggerId}>
								{#snippet children({ props })}
									<div class="flex items-center justify-between">
										<Form.Label>Purchase Order</Form.Label>
										<Dialog.Root bind:open={purchaseOrderDialogOpen}>
											<Dialog.Trigger
												type="button"
												class={cn(buttonVariants({ variant: 'secondary', size: 'sm' }))}
												>Create New Purchase Order</Dialog.Trigger
											>
											<Dialog.Content class="max-h-[90vh] max-w-4xl overflow-auto">
												<NewPurchaseOrder
													isModal={true}
													onPurchaseOrderCreated={handlePurchaseOrderCreated}
													data={{
														form: data.newPurchaseOrderForm,
														newVendorForm: data.newVendorForm,
														vendors: vendors as VendorListItem[],
														project: {
															project_id: project.project_id,
															name: project.name,
															client: {
																client_id: project.client.client_id,
																name: project.client.name,
																organization: project.client.organization,
															},
														},
													}}
												/>
											</Dialog.Content>
										</Dialog.Root>
									</div>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between',
											!purchaseOrderProxy && 'text-muted-foreground',
										)}
										role="combobox"
										{...props}
									>
										{purchaseOrderProxy
											? purchaseOrderOptions.find((f) => f.value === purchaseOrderProxy)?.label
											: 'Select a purchase order (optional)'}
										<ChevronsUpDownIcon class="opacity-50" />
									</Popover.Trigger>
									<input hidden value={purchaseOrderProxy} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content class="w-full p-0">
								<Command.Root>
									<Command.Input autofocus placeholder="Search purchase orders..." class="h-9" />
									<Command.Empty>No purchase order found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										<Command.Item
											value="No purchase order"
											onSelect={() => {
												purchaseOrderProxy = '';
												closeAndFocusTrigger(
													purchaseOrderTriggerId,
													(value) => (purchaseOrderOpen = value),
												);
											}}
										>
											No purchase order
											<CheckIcon
												class={cn('ml-auto', purchaseOrderProxy !== '' && 'text-transparent')}
											/>
										</Command.Item>
										{#each purchaseOrderOptions as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													purchaseOrderProxy = option.value;
													closeAndFocusTrigger(
														purchaseOrderTriggerId,
														(value) => (purchaseOrderOpen = value),
													);
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto',
														option.value !== purchaseOrderProxy && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Form Actions -->
				<div class="flex gap-4 pt-4">
					<Button type="submit" class="flex-1">Create Work Package</Button>
					<Button type="button" variant="outline" class="flex-1" onclick={() => history.back()}>
						Cancel
					</Button>
				</div>
			</div>
		</form>
	</div>
</div>
