import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { editInvoiceSchema } from '$lib/schemas/invoice';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name, invoice_id } = params;

	// Get project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(client_id, name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Get invoice data
	const { data: invoice, error: invoiceError } = await supabase
		.from('invoice')
		.select('*')
		.eq('invoice_id', invoice_id)
		.eq('project_id', projectData.project_id)
		.single();

	if (invoiceError || !invoice) {
		console.error('Error fetching invoice:', invoiceError);
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}/actuals`,
			{ type: 'error', message: 'Invoice not found' },
			cookies,
		);
	}

	// Get vendors accessible for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		entity_type_param: 'project',
		entity_id_param: projectData.project_id,
		user_id_param: (await requireUser()).user.id,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw new Error('Failed to fetch vendors');
	}

	// Get purchase orders for this project
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_accessible_purchase_orders',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw new Error('Failed to fetch purchase orders');
	}

	// Initialize form with invoice data
	const form = await superValidate(invoice, zod(editInvoiceSchema));

	return {
		form,
		project: projectData,
		vendors: vendors || [],
		purchaseOrders: purchaseOrders || [],
		invoice,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name, invoice_id } = params;

		const form = await superValidate(request, zod(editInvoiceSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Remove invoice_id from the update data
		const { invoice_id: _, ...updateData } = form.data;

		// Update the invoice
		const { data: invoice, error: invoiceError } = await supabase
			.from('invoice')
			.update(updateData)
			.eq('invoice_id', invoice_id)
			.select('invoice_id, po_number')
			.single();

		if (invoiceError) {
			console.error('Error updating invoice:', invoiceError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update invoice' },
			});
		}

		return redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}/actuals`,
			{ type: 'success', message: `Invoice ${invoice.po_number} updated successfully` },
			cookies,
		);
	},
};
