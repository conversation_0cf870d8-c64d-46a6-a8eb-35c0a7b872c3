import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import type {
	CostDetailBudgetItem,
	CostDetailWorkPackageData,
	CostDetailPurchaseOrderData,
} from '$lib/schemas/cost_detail';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(client_id, name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw error(404, 'Project not found');
	}

	// Fetch cost detail data using the RPC function
	const { data: costDetailData, error: costDetailError } = await supabase.rpc(
		'get_cost_detail_data',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (costDetailError) {
		console.error('Error fetching cost detail data:', costDetailError);
		throw error(500, 'Failed to fetch cost detail data');
	}

	// Fetch all WBS items for hierarchy creation
	const { data: allWbsItems, error: wbsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('wbs_library_id', projectData.wbs_library_id)
		.or(
			`client_id.eq.${projectData.client.client_id},project_id.eq.${projectData.project_id},item_type.eq.Standard`,
		);

	if (wbsError) {
		console.error('Error fetching WBS items:', wbsError);
		throw error(500, { message: 'Error loading WBS library data' });
	}

	// Transform cost detail data to match BudgetLineItemBase interface
	const transformedCostDetailData: CostDetailBudgetItem[] = (costDetailData || []).map((item) => ({
		wbs_library_item_id: item.wbs_library_item_id,
		quantity: item.quantity,
		unit_rate: item.unit_rate,
		factor: item.factor,
		wbs_code: item.wbs_code,
		wbs_description: item.wbs_description,
		wbs_level: item.wbs_level,
		parent_item_id: item.parent_item_id,
		budget_amount: item.budget_amount,
		work_packages: Array.isArray(item.work_packages)
			? (item.work_packages as unknown as CostDetailWorkPackageData[])
			: [],
		purchase_orders: Array.isArray(item.purchase_orders)
			? (item.purchase_orders as unknown as CostDetailPurchaseOrderData[])
			: [],
	}));

	return {
		project: projectData,
		costDetailData: transformedCostDetailData,
		allWbsItems: allWbsItems || [],
	};
};
