import { getProjectForClientWithStages } from '$lib/project_utils';
import type { ServerLoad } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';

export const load: ServerLoad = async ({ locals, cookies }) => {
	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	const { project, projectError } = await getProjectForClientWithStages(
		supabase,
		org_name,
		client_name,
		project_name,
	);

	if (projectError || !project) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// 	// Check if user can edit the project
	const { data: canEditProject, error: canEditProjectError } = await supabase.rpc(
		'can_modify_project',
		{
			project_id_param: project.project_id,
		},
	);

	if (canEditProjectError) {
		console.error('Error checking project edit permissions:', canEditProjectError);
	}

	const uncompleted = project.project_stage.find((stage) => !stage.date_completed);

	// Find the current active stage (first incomplete stage)
	// If all stages are completed, then there is no current stage (return undefined)
	const currentStage = project.project_stage.every((stage) => stage.date_completed)
		? undefined
		: uncompleted || project.project_stage[0];

	return {
		client_name: client_name,
		project_name: project_name,
		project: project,
		projectStages: project.project_stage,
		currentStage,
		canEditProject: canEditProject || false,
	};
};
