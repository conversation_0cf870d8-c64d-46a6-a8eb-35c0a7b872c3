<script lang="ts">
	import type { PageData } from './$types';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import { Avatar, AvatarFallback } from '$lib/components/ui/avatar';
	import * as Dialog from '$lib/components/ui/dialog';
	import GearIcon from 'phosphor-svelte/lib/Gear';
	import { enhance } from '$app/forms';
	import { invalidate } from '$app/navigation';
	import { formatDate } from '$lib/utils';

	const { data }: { data: PageData } = $props();
	const { members, invites, isAdmin } = data;

	let confirmRemoveDialog = $state(false);
	let selectedMember = $state<(typeof members)[0] | null>(null);

	let confirmCancelInviteDialog = $state(false);
	let selectedInvite = $state<NonNullable<typeof invites>[0] | null>(null);

	function showRemoveDialog(member: (typeof members)[0]) {
		selectedMember = member;
		confirmRemoveDialog = true;
	}

	function showCancelInviteDialog(invite: NonNullable<typeof invites>[0]) {
		selectedInvite = invite;
		confirmCancelInviteDialog = true;
	}
</script>

<div class="container mx-auto max-w-2xl py-16">
	<div class="mb-6 flex justify-between">
		<h1 class="text-3xl font-bold">Organization Members</h1>
		<a href="/org/{encodeURIComponent(page.params.org_name)}/settings">
			<GearIcon class="text-primary size-6" />
		</a>
	</div>
	<div class="mb-6 flex justify-end">
		{#if isAdmin}
			<Button href="/org/{encodeURIComponent(page.params.org_name)}/invite" variant="default">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="mr-2"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg
				>
				Add New Teammate
			</Button>
		{/if}
	</div>

	<div class="rounded-lg border">
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead>
					<tr class="bg-muted/50 border-b">
						<th class="px-6 py-3 text-left font-medium">User</th>
						<th class="px-6 py-3 text-left font-medium">Role</th>
						<th class="px-6 py-3 text-left font-medium">Status</th>
						<th class="py-3 pr-6 text-right font-medium">Actions</th>
					</tr>
				</thead>
				<tbody>
					{#each members as member (member.membership_id)}
						<tr class="hover:bg-muted/50 border-b">
							<td class="px-6 py-3">
								<div class="flex items-center">
									<Avatar class="mr-3 size-10">
										<AvatarFallback>
											{member.profile.full_name
												? member.profile.full_name
														.split(' ')
														.map((n) => n[0])
														.join('')
														.toUpperCase()
												: 'U'}
										</AvatarFallback>
									</Avatar>
									<div>
										<div class="font-medium">{member.profile.full_name || 'Unnamed User'}</div>
										<div class="text-sm text-gray-500">{member.profile.email}</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-3">
								<span class="capitalize">{member.role}</span>
							</td>
							<td class="px-6 py-3">
								<span
									class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
								>
									Active
								</span>
							</td>
							<td class="px-6 py-3 text-right">
								{#if isAdmin && member.profile.user_id !== page.data.session?.user.id}
									<Button variant="ghost" size="sm" onclick={() => showRemoveDialog(member)}>
										Remove
									</Button>
								{/if}
							</td>
						</tr>
					{/each}

					{#each invites || [] as invite (invite.invite_id)}
						<tr class="hover:bg-muted/50 border-b">
							<td class="px-6 py-3">
								<div>
									<div class="font-medium">{invite.invitee_email}</div>
									<div class="text-sm text-gray-500">
										Invited on {formatDate(invite.created_at)}
									</div>
								</div>
							</td>
							<td class="px-6 py-3">
								<span class="capitalize">{invite.role}</span>
							</td>
							<td class="px-6 py-3">
								<span
									class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800"
								>
									Pending
								</span>
							</td>
							<td class="px-6 py-3 text-right">
								{#if isAdmin}
									<Button variant="ghost" size="sm" onclick={() => showCancelInviteDialog(invite)}>
										Cancel
									</Button>
								{/if}
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</div>
</div>

<Dialog.Root bind:open={confirmRemoveDialog}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Remove Member</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to remove {selectedMember?.profile.full_name ||
					selectedMember?.profile.email} from this organization?
			</Dialog.Description>
		</Dialog.Header>
		<form method="POST" action="?/remove" use:enhance>
			<input type="hidden" name="memberId" value={selectedMember?.membership_id} />
			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (confirmRemoveDialog = false)}>
					Cancel
				</Button>
				<Button type="submit" variant="destructive">Remove</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>

<Dialog.Root bind:open={confirmCancelInviteDialog}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Cancel Invitation</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to cancel the invitation for {selectedInvite?.invitee_email}?
			</Dialog.Description>
		</Dialog.Header>
		<div class="py-4">
			<p class="text-muted-foreground text-sm">
				This action will revoke the invitation. The user will no longer be able to join the
				organization.
			</p>
		</div>
		<form
			method="POST"
			action="?/cancelInvite"
			use:enhance={() => {
				return ({ result }) => {
					if (result.type === 'success') {
						confirmCancelInviteDialog = false;
						invalidate('/org/[org_name]/members');
					}
				};
			}}
		>
			<input type="hidden" name="inviteId" value={selectedInvite?.invite_id} />
			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (confirmCancelInviteDialog = false)}>
					Cancel
				</Button>
				<Button type="submit" variant="destructive">Revoke Invitation</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
