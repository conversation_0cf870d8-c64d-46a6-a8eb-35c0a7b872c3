import type { LayoutServerLoad } from './$types';
import { loadFlash } from 'sveltekit-flash-message/server';
import { SIDEBAR_COOKIE_NAME } from '$lib/components/ui/sidebar/constants.js';
import { ORG_COOKIE_NAME } from '$lib/current-org.svelte';

export const load: LayoutServerLoad = loadFlash(async ({ locals, depends, cookies }) => {
	depends('sidebar:clients');
	const { supabase, orgId, user, session } = locals;

	// Verify orgId from cookie if user is authenticated
	if (user && orgId) {
		// Check if the user still has access to the organization
		const { data: hasAccess, error: accessError } = await supabase.rpc(
			'current_user_has_entity_access',
			{
				entity_type_param: 'organization',
				entity_id_param: orgId,
			},
		);

		// If user no longer has access or there was an error, clear the cookie
		if (accessError || !hasAccess) {
			console.log('Clearing org cookie - user no longer has access or org not found');
			cookies.delete(ORG_COOKIE_NAME, { path: '/' });
			// Update locals to reflect the cleared orgId
			locals.orgId = null;
		}
	}

	// Fetch all clients for the sidebar
	const { data: clients, error } = await supabase
		.from('client')
		.select('client_id, name, organization(name)')
		.order('name');
	if (error) {
		console.error('Error fetching clients for sidebar:', error);
	}

	const { data: profile, error: profileError } = user
		? await supabase.from('profile').select('*').eq('user_id', user.id).single()
		: { data: null, error: null };
	if (profileError) throw profileError;

	// Read sidebar state from cookie, default to false (closed)
	const sidebarCookie = cookies.get(SIDEBAR_COOKIE_NAME);
	const sidebarOpen = sidebarCookie === 'true';

	return {
		session,
		user,
		profile,
		cookies: cookies.getAll(),
		sidebarClients: clients || [],
		sidebarOpen,
	};
});
