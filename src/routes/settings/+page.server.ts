import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { message, superValidate } from 'sveltekit-superforms/server';

import { profileSchema } from '$lib/schemas/profile';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals }) => {
	const { user } = await requireUser();

	const { data: profile, error } = await locals.supabase
		.from('profile')
		.select('*')
		.eq('user_id', user.id)
		.single();
	if (error) throw error;

	const form = await superValidate(
		{
			...profile,
			avatar_url: profile.avatar_url ?? undefined,
			full_name: profile.full_name ?? undefined,
		},
		zod(profileSchema),
	);
	return { form, email: profile.email };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const { user } = await requireUser();

		const form = await superValidate(request, zod(profileSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { error: updateError } = await locals.supabase
			.from('profile')
			.update({
				email: form.data.email,
				full_name: form.data.full_name,
				avatar_url: form.data.avatar_url,
			})
			.eq('user_id', user.id);
		if (updateError) {
			return fail(500, { form, message: updateError.message });
		}
		return message(form, { type: 'success', text: 'Profile updated successfully.' });
	},
};
