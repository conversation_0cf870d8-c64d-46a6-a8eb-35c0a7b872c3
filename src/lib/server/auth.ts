import { getRequestEvent } from '$app/server';
import { redirect } from 'sveltekit-flash-message/server';

/**
 * Ensure a user is present in the current request. If not, redirect to the
 * sign in page with a `next` parameter that points back to the current path.
 */
export const requireUser = async () => {
	const { url, locals, cookies } = getRequestEvent();
	const { user } = await locals.getSession();
	const path = url.pathname || '/';
	if (!user) {
		throw redirect(
			`/auth/signin?next=${encodeURIComponent(path)}`,
			{ type: 'error', message: 'Please sign in to view.' },
			cookies,
		);
	}
	return { user };
};

/**
 * Ensure the `client_name` param is present for the current request.
 * If missing, redirect to the client's list for the organization.
 */
export const requireClient = () => {
	const { params, cookies } = getRequestEvent();
	const { client_name, org_name } = params;

	if (!org_name) {
		throw redirect('/', { type: 'error', message: 'Organization not found' }, cookies);
	}

	if (!client_name) {
		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients`,
			{ type: 'error', message: 'Client not found' },
			cookies,
		);
	}

	return { org_name, client_name };
};

/**
 * Ensure the `project_name` param is present for the current request.
 * If missing, redirect to the project's client page.
 */
export const requireProject = () => {
	const { params, cookies } = getRequestEvent();

	const { org_name, client_name } = requireClient();
	const { project_name } = params;

	if (!project_name) {
		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	return { org_name, client_name, project_name };
};
