import type { Database } from '$lib/database.types';

// Type definition for work package data in cost detail context
// This is different from the WorkPackageData in work_package.ts as it includes
// purchase order and vendor information for cost detail display
export interface CostDetailWorkPackageData {
	work_package_id: string;
	name: string;
	description: string | null;
	purchase_order_id: string | null;
	purchase_order_number: string | null;
	vendor_name: string | null;
}

// Type definition for purchase order data in cost detail context
// Includes cost-related fields for cost detail display
export interface CostDetailPurchaseOrderData {
	purchase_order_id: string;
	po_number: string;
	description: string | null;
	vendor_name: string | null;
	original_amount: number | null;
	co_amount: number | null;
}

// Type definition for cost detail data from RPC function
// Extends the database function return type with typed arrays
export type CostDetailItem =
	Database['public']['Functions']['get_cost_detail_data']['Returns'][0] & {
		work_packages: CostDetailWorkPackageData[];
		purchase_orders: CostDetailPurchaseOrderData[];
	};

// Extended interface for cost detail items that implements BudgetLineItemBase
// This provides a unified interface for budget-related operations in cost detail
export interface CostDetailBudgetItem {
	wbs_library_item_id: string;
	quantity: number | null;
	unit_rate: number | null;
	factor: number | null;
	// Additional cost detail specific fields
	wbs_code: string;
	wbs_description: string;
	wbs_level: number;
	parent_item_id: string | null;
	budget_amount: number | null;
	work_packages: CostDetailWorkPackageData[];
	purchase_orders: CostDetailPurchaseOrderData[];
}
