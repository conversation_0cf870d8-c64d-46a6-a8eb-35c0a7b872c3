import { z } from 'zod';

// Base invoice schema for creation
export const invoiceSchema = z.object({
	purchase_order_id: z.uuid('Please select a purchase order'),
	vendor_id: z.uuid('Please select a vendor'),
	description: z.string().optional().nullable(),
	invoice_date: z.string().min(1, 'Invoice date is required'),
	account: z.string().min(1, 'Account is required'),
	amount: z.number().min(0.01, 'Amount must be greater than 0'),
	period: z.string().optional().nullable(),
	post_date: z.string().min(1, 'Post date is required'),
	notes: z.string().optional().nullable(),
	project_id: z.uuid(),
});

// Edit invoice schema - includes invoice_id for updates
export const editInvoiceSchema = z.object({
	invoice_id: z.uuid(),
	purchase_order_id: z.uuid('Please select a purchase order'),
	vendor_id: z.uuid('Please select a vendor'),
	description: z.string().optional().nullable(),
	invoice_date: z.string().min(1, 'Invoice date is required'),
	account: z.string().min(1, 'Account is required'),
	amount: z.number().min(0.01, 'Amount must be greater than 0'),
	period: z.string().optional().nullable(),
	post_date: z.string().min(1, 'Post date is required'),
	notes: z.string().optional().nullable(),
	project_id: z.uuid(),
});

// Modal version for inline creation (without project_id as it's provided by context)
export const invoiceModalSchema = z.object({
	purchase_order_id: z.uuid('Please select a purchase order'),
	vendor_id: z.uuid('Please select a vendor'),
	description: z.string().optional().nullable(),
	invoice_date: z.string().min(1, 'Invoice date is required'),
	account: z.string().min(1, 'Account is required'),
	amount: z.number().min(0.01, 'Amount must be greater than 0'),
	period: z.string().optional().nullable(),
	post_date: z.string().min(1, 'Post date is required'),
	notes: z.string().optional().nullable(),
});

// Type exports
export type InvoiceSchema = z.infer<typeof invoiceSchema>;
export type EditInvoiceSchema = z.infer<typeof editInvoiceSchema>;
export type InvoiceModalSchema = z.infer<typeof invoiceModalSchema>;

// Type for invoice data from database (includes timestamps and IDs)
export type InvoiceData = {
	invoice_id: string;
	purchase_order_id: string;
	vendor_id: string;
	description: string | null;
	invoice_date: string;
	account: string;
	amount: number;
	period: string | null;
	post_date: string;
	notes: string | null;
	project_id: string;
	created_by_user_id: string;
	created_at: string;
	updated_at: string;
};

// Type for invoice list items (with joined vendor and purchase order information)
// This matches the return type from get_accessible_invoices RPC function
export type InvoiceListItem = {
	invoice_id: string;
	purchase_order_id: string;
	po_number: string;
	description: string | null;
	invoice_date: string;
	vendor_name: string;
	vendor_id: string;
	account: string;
	amount: number;
	period: string | null;
	post_date: string;
	notes: string | null;
	created_at: string;
	created_by_name: string;
};

// Common accounting periods for dropdown
export const accountingPeriods = [
	'January',
	'February',
	'March',
	'April',
	'May',
	'June',
	'July',
	'August',
	'September',
	'October',
	'November',
	'December',
] as const;

// Common account codes (can be customized per organization)
export const commonAccountCodes = [
	'1000 - Cash',
	'1200 - Accounts Receivable',
	'1500 - Inventory',
	'2000 - Accounts Payable',
	'2100 - Accrued Expenses',
	'3000 - Owner Equity',
	'4000 - Revenue',
	'5000 - Cost of Goods Sold',
	'6000 - Operating Expenses',
	'6100 - Office Expenses',
	'6200 - Professional Services',
	'6300 - Travel & Entertainment',
	'6400 - Utilities',
	'6500 - Insurance',
	'6600 - Repairs & Maintenance',
	'7000 - Other Income',
	'8000 - Other Expenses',
] as const;

// Currency formatting utility
export function formatCurrency(amount: number, currency: string = 'SEK'): string {
	return new Intl.NumberFormat('sv-SE', {
		style: 'currency',
		currency: currency,
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(amount);
}

// Date formatting utility
export function formatDate(dateString: string): string {
	return new Date(dateString).toLocaleDateString('en-GB', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	});
}
