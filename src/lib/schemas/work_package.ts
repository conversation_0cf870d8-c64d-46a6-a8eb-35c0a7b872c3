import { z } from 'zod';

// Work package schema for creation
export const workPackageSchema = z.object({
	name: z.string().min(1, 'Work package name is required'),
	description: z.string().optional().nullable(),
	purchase_order_id: z.uuid().optional().nullable(),
	wbs_library_item_id: z.uuid('Please select a WBS library item'),
});

// Edit work package schema - includes work_package_id for updates
export const editWorkPackageSchema = z.object({
	work_package_id: z.uuid(),
	name: z.string().min(1, 'Work package name is required'),
	description: z.string().optional().nullable(),
	purchase_order_id: z.uuid().optional().nullable(),
	wbs_library_item_id: z.uuid('Please select a WBS library item'),
});

// Type definitions
export type WorkPackageSchema = z.infer<typeof workPackageSchema>;
export type EditWorkPackageSchema = z.infer<typeof editWorkPackageSchema>;

// Type for work package data from database (includes timestamps and IDs)
export type WorkPackageData = {
	work_package_id: string;
	name: string;
	description: string | null;
	project_id: string;
	purchase_order_id: string | null;
	wbs_library_item_id: string;
	created_at: string;
	updated_at: string;
};

// Type for work package with related information (from RPC function)
export type WorkPackageWithRelations = {
	work_package_id: string;
	name: string;
	description: string | null;
	project_id: string;
	purchase_order_id: string | null;
	purchase_order_number: string | null;
	wbs_library_item_id: string;
	wbs_code: string | null;
	wbs_description: string | null;
	created_at: string;
	updated_at: string;
};

// Type for work package list item (simplified for display)
export type WorkPackageListItem = {
	work_package_id: string;
	name: string;
	description: string | null;
	purchase_order_number: string | null;
	wbs_code: string | null;
	wbs_description: string | null;
	created_at: string;
};

// Type for purchase order selection
export type PurchaseOrderSelectItem = {
	purchase_order_id: string;
	po_number: string;
	description: string | null;
	vendor_name: string | null;
};

// Type for WBS library item selection
export type WbsLibraryItemSelectItem = {
	wbs_library_item_id: string;
	code: string;
	description: string;
	level: number;
	parent_item_id: string | null;
};
