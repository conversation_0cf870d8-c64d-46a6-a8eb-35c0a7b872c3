import { fail, type RequestEvent } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { purchaseOrderSchema, purchaseOrderModalSchema } from '$lib/schemas/purchase_order';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject, requireUser } from '$lib/server/auth';

export const createPurchaseOrder = async ({ request, locals, cookies, params }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = params;

	if (!org_name || !client_name || !project_name) {
		return fail(400, { message: { type: 'error', text: 'Missing required parameters' } });
	}

	const form = await superValidate(request, zod(purchaseOrderSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Get project_id from URL params
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
	}

	// Prepare the purchase order data
	const purchaseOrderData = {
		...form.data,
		project_id: projectData.project_id,
		created_by_user_id: user.id,
	};

	// Insert the purchase order
	const { data: purchaseOrder, error: purchaseOrderError } = await supabase
		.from('purchase_order')
		.insert(purchaseOrderData)
		.select('purchase_order_id, po_number')
		.single();

	if (purchaseOrderError) {
		console.error('Error creating purchase order:', purchaseOrderError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create purchase order' },
		});
	}

	throw redirect(
		302,
		`../purchase-order/${purchaseOrder.purchase_order_id}`,
		{
			type: 'success',
			message: `Purchase order "${purchaseOrder.po_number}" created successfully`,
		},
		cookies,
	);
};

export const createPurchaseOrderModal = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project_id
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return fail(404, { message: { type: 'error', text: 'Project not found' } });
	}

	const form = await superValidate(request, zod(purchaseOrderModalSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Set the project_id in the form data
	form.data.project_id = projectData.project_id;

	// Prepare the purchase order data
	const purchaseOrderData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the purchase order and get vendor name
	const { data: purchaseOrder, error: purchaseOrderError } = await supabase
		.from('purchase_order')
		.insert(purchaseOrderData)
		.select(
			`
				purchase_order_id,
				po_number,
				description,
				po_date,
				vendor_id,
				account,
				original_amount,
				co_amount,
				freight,
				tax,
				other,
				notes,
				vendor:vendor_id (name)
			`,
		)
		.single();

	if (purchaseOrderError) {
		console.error('Error creating purchase order:', purchaseOrderError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create purchase order' },
		});
	}

	// Return success with purchase order data instead of redirecting
	return {
		form: message(form, {
			type: 'success',
			text: `Purchase order "${purchaseOrder.po_number}" created successfully`,
		}),
		purchaseOrder: {
			purchase_order_id: purchaseOrder.purchase_order_id,
			po_number: purchaseOrder.po_number,
			description: purchaseOrder.description,
			po_date: purchaseOrder.po_date,
			vendor_id: purchaseOrder.vendor_id,
			vendor_name: purchaseOrder.vendor?.name || '',
			account: purchaseOrder.account,
			original_amount: purchaseOrder.original_amount,
			co_amount: purchaseOrder.co_amount,
			freight: purchaseOrder.freight,
			tax: purchaseOrder.tax,
			other: purchaseOrder.other,
			notes: purchaseOrder.notes,
		},
	};
};
