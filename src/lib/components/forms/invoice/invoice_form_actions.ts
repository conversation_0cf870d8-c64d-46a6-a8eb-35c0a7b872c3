import { fail } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { invoiceSchema, invoiceModalSchema } from '$lib/schemas/invoice';
import { requireUser } from '$lib/server/auth';
import type { RequestEvent } from '@sveltejs/kit';

export const createInvoice = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(invoiceSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the invoice data
	const invoiceData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the invoice
	const { data: invoice, error: invoiceError } = await supabase
		.from('invoice')
		.insert(invoiceData)
		.select(
			`
			invoice_id,
			po_number,
			description,
			invoice_date,
			account,
			amount,
			period,
			post_date,
			notes,
			created_at,
			vendor:vendor_id(name)
		`,
		)
		.single();

	if (invoiceError) {
		console.error('Error creating invoice:', invoiceError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create invoice' },
		});
	}

	return message(form, {
		type: 'success',
		text: `Invoice ${invoice.po_number} created successfully`,
	});
};

export const createInvoiceModal = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(invoiceModalSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Get project_id from the URL or context
	// This should be provided by the calling component
	const url = new URL(request.url);
	const pathSegments = url.pathname.split('/');
	const projectNameIndex = pathSegments.findIndex((segment) => segment === 'projects') + 1;
	const projectName = pathSegments[projectNameIndex];

	if (!projectName) {
		return fail(400, {
			form,
			message: { type: 'error', text: 'Project context not found' },
		});
	}

	// Get project_id from project name
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('project_id')
		.eq('name', decodeURIComponent(projectName))
		.single();

	if (projectError || !project) {
		return fail(400, {
			form,
			message: { type: 'error', text: 'Project not found' },
		});
	}

	// Prepare the invoice data
	const invoiceData = {
		...form.data,
		project_id: project.project_id,
		created_by_user_id: user.id,
	};

	// Insert the invoice
	const { data: invoice, error: invoiceError } = await supabase
		.from('invoice')
		.insert(invoiceData)
		.select(
			`
			invoice_id,
			po_number,
			description,
			invoice_date,
			account,
			amount,
			period,
			post_date,
			notes,
			created_at,
			vendor:vendor_id(name)
		`,
		)
		.single();

	if (invoiceError) {
		console.error('Error creating invoice:', invoiceError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create invoice' },
		});
	}

	// Return the invoice data for modal handling
	return {
		form,
		invoice: {
			invoice_id: invoice.invoice_id,
			po_number: invoice.po_number,
			description: invoice.description,
			invoice_date: invoice.invoice_date,
			vendor_name: invoice.vendor?.name || 'Unknown Vendor',
			vendor_id: invoiceData.vendor_id,
			account: invoice.account,
			amount: invoice.amount,
			period: invoice.period,
			post_date: invoice.post_date,
			notes: invoice.notes,
			created_at: invoice.created_at,
			created_by_name: user.email || 'Unknown User',
		},
		message: { type: 'success', text: `Invoice ${invoice.po_number} created successfully` },
	};
};
