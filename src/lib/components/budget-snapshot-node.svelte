<script lang="ts">
	import BudgetSnapshotNode from '$lib/components/budget-snapshot-node.svelte';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { formatCurrency, formatPercentage } from '$lib/utils';
	import { SvelteSet } from 'svelte/reactivity';
	import type { UnifiedBudgetNode } from '$lib/budget_utils';

	let {
		node,
		indent,
		expanded,
		toggle,
		hideZeros,
	}: {
		node: UnifiedBudgetNode;
		indent: number;
		expanded: SvelteSet<string>;
		toggle: (nodeId: string) => void;
		hideZeros: boolean;
	} = $props();

	// true if this node is in the expanded set
	const isOpen = $derived(node.id && !expanded.has(node.id));

	// Called when the caret is clicked:
	function onToggle() {
		if (node.id) toggle(node.id);
	}
</script>

{#if !hideZeros || node.value || node.data.subtotal > 0}
	<!-- Show total cost for nodes with children or no snapshot data -->
	{#if (node.value !== node.data.subtotal && (node.children?.length || 0) > 0) || !node.data.budgetData}
		<tr
			class="group hover:bg-muted/20 border-b {(node.children?.length || 0) > 0
				? 'font-medium'
				: ''}"
		>
			<td class="py-3 pr-2 pl-8">
				<div class="flex items-center">
					{#if node.children?.length}
						<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
							{#if isOpen}
								<CaretDownIcon class="size-4" />
							{:else}
								<CaretRightIcon class="size-4" />
							{/if}
						</button>
						<span class="ml-2">{node.data.code}</span>
					{:else}
						<span class="ml-6">{node.data.code}</span>
					{/if}
				</div>
			</td>
			<td class="p-1" style="padding-left: {0.5 + indent * 0.75}rem">
				{node.data.description}
			</td>
			<td class="p-1 text-right"></td>
			<td class="p-1"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right">
				{node.data.budgetData?.factor && node.data.budgetData.factor !== 1
					? node.data.budgetData.factor
					: ''}
			</td>
			<td class="p-1 text-right">
				{formatCurrency(node.value)}
			</td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
		</tr>
	{/if}

	<!-- Display snapshot data if it exists -->
	{#if node.data.budgetData}
		{@const snapshotData = node.data.budgetData}
		{#if snapshotData.quantity || snapshotData.unit || snapshotData.unit_rate}
			<tr class="group hover:bg-muted/20 border-b">
				<td class="py-3 pr-2 pl-8">
					<div class="flex items-center">
						{#if node.children?.length && !hideZeros}
							<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
								{#if isOpen}
									<CaretDownIcon class="size-4" />
								{:else}
									<CaretRightIcon class="size-4" />
								{/if}
							</button>
							<span class="ml-2">{node.data.code}</span>
						{:else}
							<span class="ml-6">{node.data.code}</span>
						{/if}
					</div>
				</td>
				<td class="p-1" style="padding-left: {0.5 + indent * 0.75}rem">
					{node.data.description}
				</td>
				<td class="p-1 text-right">
					{snapshotData.quantity}
				</td>
				<td class="p-1">
					{snapshotData.unit ?? ''}
				</td>
				<td class="p-1 text-right">
					{#if !snapshotData.unit_rate_manual_override}
						{formatCurrency(snapshotData.material_rate)}
					{/if}
				</td>
				<td class="p-1 text-right">
					{#if !snapshotData.unit_rate_manual_override}
						{formatCurrency(snapshotData.labor_rate ?? 0)}
					{/if}
				</td>
				<td class="p-1 text-right">
					{#if !snapshotData.unit_rate_manual_override}
						{formatCurrency(snapshotData.productivity_per_hour ?? 0)}
					{/if}
				</td>
				<td class="p-1 text-right">
					{formatCurrency(snapshotData.unit_rate)}
				</td>
				<td class="p-1 text-right">
					{snapshotData.factor ?? ''}
				</td>
				<td class="p-1 text-right">
					{formatCurrency(node.data.subtotal)}
				</td>
				<td class="p-1 text-center">
					{snapshotData.cost_certainty != null ? formatPercentage(snapshotData.cost_certainty) : ''}
				</td>
				<td class="p-1 text-center">
					{snapshotData.design_certainty != null
						? formatPercentage(snapshotData.design_certainty)
						: ''}
				</td>
			</tr>
		{/if}
	{/if}

	<!-- Only render children when this node is open -->
	{#if isOpen && node.children}
		{#each node.children as child (child.id)}
			<BudgetSnapshotNode node={child} indent={indent + 1} {expanded} {toggle} {hideZeros} />
		{/each}
	{/if}
{/if}
