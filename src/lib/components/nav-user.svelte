<script lang="ts">
	import { page } from '$app/state';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { Avatar, AvatarImage, AvatarFallback } from '$lib/components/ui/avatar/index.js';
	import SignOutIcon from 'phosphor-svelte/lib/SignOut';
	import GearIcon from 'phosphor-svelte/lib/Gear';
	import { type Tables } from '$lib/database.types';
	import { goto } from '$app/navigation';

	const sidebar = Sidebar.useSidebar();

	const { profile }: { profile: Tables<'profile'> } = $props();

	async function signOut() {
		// Clear org cookie by calling the server endpoint
		await fetch('/api/auth/signout', { method: 'POST' });

		// Sign out from Supabase
		const supabase = page.data.supabase;
		const { error } = await supabase.auth.signOut();
		if (error) console.error('Sign out error:', error);

		// Redirect to sign-in page
		goto('/auth/signin');
	}
</script>

<Sidebar.Menu>
	<Sidebar.MenuItem>
		<div class="shrink-0">
			<DropdownMenu.Root>
				<DropdownMenu.Trigger id="profile-button" class={sidebar.open ? 'p-2' : 'px-px py-2'}>
					<div class="flex w-full items-center justify-center gap-3">
						<Avatar class="size-10">
							{#if profile?.avatar_url}
								<AvatarImage src={profile.avatar_url} alt={profile?.full_name} />
							{/if}
							<AvatarFallback>{profile?.full_name?.[0]?.toUpperCase() ?? 'U'}</AvatarFallback>
						</Avatar>
						{#if sidebar.open}
							<div class="grid flex-1 text-left text-sm leading-tight">
								<span class="truncate font-semibold">{profile?.full_name}</span>
								<span class="text-muted-foreground truncate text-xs">{profile?.email}</span>
							</div>
						{/if}
					</div>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content class="w-56" sideOffset={16} alignOffset={8} align="start">
					<div class="flex items-center gap-3 p-3">
						<Avatar class="size-10">
							{#if profile?.avatar_url}
								<AvatarImage src={profile.avatar_url} alt={profile?.full_name} />
							{/if}
							<AvatarFallback>{profile?.full_name?.[0]?.toUpperCase() ?? 'U'}</AvatarFallback>
						</Avatar>
						<div class="grid flex-1 text-left text-sm leading-tight">
							<span class="truncate font-semibold">{profile?.full_name}</span>
							<span class="text-muted-foreground truncate text-xs">{profile?.email}</span>
						</div>
					</div>

					<DropdownMenu.Separator />

					<a
						href="/settings"
						class="hover:bg-accent hover:text-accent-foreground flex w-full cursor-pointer items-center px-2 py-1.5 text-sm no-underline"
					>
						<GearIcon class="mr-2 size-4" />
						Settings
					</a>

					<DropdownMenu.Separator />

					<button
						class="hover:bg-accent hover:text-accent-foreground flex w-full cursor-pointer items-center px-2 py-1.5 text-sm"
						onclick={signOut}
					>
						<SignOutIcon class="mr-2 size-4" />
						Sign out
					</button>
				</DropdownMenu.Content>
			</DropdownMenu.Root>
		</div>
	</Sidebar.MenuItem>
</Sidebar.Menu>
