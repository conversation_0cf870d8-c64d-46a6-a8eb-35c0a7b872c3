-- Vendor Table Schema
-- Contains vendor information with hierarchical access control
-- Vendors can be associated with organization, client, or project level
-- Vendor table with hierarchical access control
CREATE TABLE IF NOT EXISTS "public"."vendor" (
	"vendor_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	-- Hierarchical access control - exactly one must be non-null
	"org_id" "uuid",
	"client_id" "uuid",
	"project_id" "uuid",
	-- Contact information
	"contact_name" "text",
	"contact_email" "text",
	"contact_phone" "text",
	"contact_address" "text",
	"website" "text",
	-- Vendor-specific fields
	"vendor_type" "text",
	"tax_id" "text",
	"payment_terms" "text",
	"payment_terms_days" integer,
	"credit_limit" numeric(15, 2),
	"currency" "text" DEFAULT 'USD',
	"is_active" boolean DEFAULT true,
	"certification_info" "jsonb",
	"insurance_info" "jsonb",
	"additional_data" "jsonb",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."vendor" OWNER TO "postgres";

COMMENT ON TABLE "public"."vendor" IS 'Vendor information with hierarchical access control. Vendors can be associated with organization, client, or project level.';

COMMENT ON COLUMN "public"."vendor"."org_id" IS 'Organization ID - if set, vendor is available to all clients/projects in this organization';

COMMENT ON COLUMN "public"."vendor"."client_id" IS 'Client ID - if set, vendor is available to all projects for this client';

COMMENT ON COLUMN "public"."vendor"."project_id" IS 'Project ID - if set, vendor is only available to this specific project';

COMMENT ON COLUMN "public"."vendor"."vendor_type" IS 'Type of vendor (e.g., Supplier, Contractor, Consultant, Service Provider)';

COMMENT ON COLUMN "public"."vendor"."payment_terms" IS 'Payment terms description (e.g., Net 30, COD, 2/10 Net 30)';

COMMENT ON COLUMN "public"."vendor"."payment_terms_days" IS 'Number of days for payment terms';

COMMENT ON COLUMN "public"."vendor"."certification_info" IS 'JSON object containing certification details (name, expiry_date, issuer, etc.)';

COMMENT ON COLUMN "public"."vendor"."insurance_info" IS 'JSON object containing insurance details (type, coverage_amount, expiry_date, etc.)';

COMMENT ON COLUMN "public"."vendor"."additional_data" IS 'JSON object for additional vendor-specific data as key-value pairs';

-- Primary key constraint
ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_pkey" PRIMARY KEY ("vendor_id");

-- Unique constraint on name within scope (organization/client/project)
ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_name_scope_unique" UNIQUE ("name", "org_id", "client_id", "project_id");

-- Check constraint to ensure exactly one scope is set
ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_single_scope_check" CHECK (
	(
		("org_id" IS NOT NULL)::integer + ("client_id" IS NOT NULL)::integer + ("project_id" IS NOT NULL)::integer
	) = 1
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "public"."organization" ("org_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."client" ("client_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."vendor"
ADD CONSTRAINT "vendor_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "vendor_org_id_idx" ON "public"."vendor" USING "btree" ("org_id")
WHERE
	"org_id" IS NOT NULL;

CREATE INDEX "vendor_client_id_idx" ON "public"."vendor" USING "btree" ("client_id")
WHERE
	"client_id" IS NOT NULL;

CREATE INDEX "vendor_project_id_idx" ON "public"."vendor" USING "btree" ("project_id")
WHERE
	"project_id" IS NOT NULL;

CREATE INDEX "vendor_name_idx" ON "public"."vendor" USING "btree" ("name");

CREATE INDEX "vendor_vendor_type_idx" ON "public"."vendor" USING "btree" ("vendor_type")
WHERE
	"vendor_type" IS NOT NULL;

CREATE INDEX "vendor_is_active_idx" ON "public"."vendor" USING "btree" ("is_active");

CREATE INDEX "vendor_created_by_user_id_idx" ON "public"."vendor" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."vendor" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."vendor" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for vendor table (function defined in shared/audit_functions.sql)
CREATE OR REPLACE TRIGGER "audit_vendor_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."vendor" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_vendor_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view vendors they have access to through hierarchy
CREATE POLICY "Users can view vendors they have access to" ON "public"."vendor" FOR
SELECT
	TO "authenticated" USING (
		(
			-- Organization-level vendors: user has access to the organization
			(
				"org_id" IS NOT NULL
				AND "public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
			)
			OR
			-- Client-level vendors: user has access to the client or its organization
			(
				"client_id" IS NOT NULL
				AND (
					"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
					OR EXISTS (
						SELECT
							1
						FROM
							"public"."client" "c"
						WHERE
							"c"."client_id" = "vendor"."client_id"
							AND "public"."current_user_has_entity_access" (
								'organization'::"public"."entity_type",
								"c"."org_id"
							)
					)
				)
			)
			OR
			-- Project-level vendors: user has access to the project, its client, or organization
			(
				"project_id" IS NOT NULL
				AND (
					"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
					OR EXISTS (
						SELECT
							1
						FROM
							"public"."project" "p"
							JOIN "public"."client" "c" ON "p"."client_id" = "c"."client_id"
						WHERE
							"p"."project_id" = "vendor"."project_id"
							AND (
								"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "p"."client_id")
								OR "public"."current_user_has_entity_access" (
									'organization'::"public"."entity_type",
									"c"."org_id"
								)
							)
					)
				)
			)
		)
	);

-- INSERT policies - users can create vendors at levels they have admin access to or are an editor of a project
CREATE POLICY "Users can create vendors at levels they have admin access to" ON "public"."vendor" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			-- Organization-level vendors: user has admin access to the organization
			(
				"org_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"org_id",
					'admin'::"public"."membership_role"
				)
			)
			OR
			-- Client-level vendors: user has admin access to the client
			(
				"client_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"client_id",
					'admin'::"public"."membership_role"
				)
			)
			OR
			-- Project-level vendors: user has editor access to the project
			(
				"project_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					"project_id",
					'editor'::"public"."membership_role"
				)
			)
		)
		AND "created_by_user_id" = (
			select
				"auth"."uid" ()
		)
	);

-- UPDATE policies - users can update vendors at levels they have admin access to
CREATE POLICY "Users can update vendors at levels they have admin access to" ON "public"."vendor"
FOR UPDATE
	TO "authenticated" USING (
		(
			-- Organization-level vendors: user has admin access to the organization
			(
				"org_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"org_id",
					'admin'::"public"."membership_role"
				)
			)
			OR
			-- Client-level vendors: user has admin access to the client
			(
				"client_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"client_id",
					'admin'::"public"."membership_role"
				)
			)
			OR
			-- Project-level vendors: user has editor access to the project
			(
				"project_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					"project_id",
					'editor'::"public"."membership_role"
				)
			)
		)
	)
WITH
	CHECK (
		(
			-- Organization-level vendors: user has admin access to the organization
			(
				"org_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"org_id",
					'admin'::"public"."membership_role"
				)
			)
			OR
			-- Client-level vendors: user has admin access to the client
			(
				"client_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"client_id",
					'admin'::"public"."membership_role"
				)
			)
			OR
			-- Project-level vendors: user has editor access to the project
			(
				"project_id" IS NOT NULL
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					"project_id",
					'editor'::"public"."membership_role"
				)
			)
		)
	);

-- DELETE policies - users can delete vendors at levels they have admin access to
CREATE POLICY "Users can delete vendors at levels they have admin access to" ON "public"."vendor" FOR DELETE TO "authenticated" USING (
	(
		-- Organization-level vendors: user has admin access to the organization
		(
			"org_id" IS NOT NULL
			AND "public"."current_user_has_entity_role" (
				'organization'::"public"."entity_type",
				"org_id",
				'admin'::"public"."membership_role"
			)
		)
		OR
		-- Client-level vendors: user has admin access to the client
		(
			"client_id" IS NOT NULL
			AND "public"."current_user_has_entity_role" (
				'client'::"public"."entity_type",
				"client_id",
				'admin'::"public"."membership_role"
			)
		)
		OR
		-- Project-level vendors: user has admin access to the project
		(
			"project_id" IS NOT NULL
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				"project_id",
				'admin'::"public"."membership_role"
			)
		)
	)
);

-- Table-specific functions
-- Function to get vendors accessible to a user at a specific scope
CREATE OR REPLACE FUNCTION "public"."get_accessible_vendors" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS TABLE (
	"vendor_id" "uuid",
	"name" "text",
	"description" "text",
	"vendor_type" "text",
	"contact_name" "text",
	"contact_email" "text",
	"contact_phone" "text",
	"is_active" boolean,
	"access_level" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	-- Check if user has access to the specified entity
	IF NOT public.has_entity_access(user_id_param, entity_type_param, entity_id_param) THEN
		RETURN;
	END IF;

	-- Return vendors based on hierarchy
	IF entity_type_param = 'project' THEN
		-- For projects, return project-level, client-level, and organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.project_id IS NOT NULL THEN 'project'
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		JOIN public.project p ON p.project_id = entity_id_param
		JOIN public.client c ON p.client_id = c.client_id
		WHERE (
			v.project_id = entity_id_param
			OR v.client_id = p.client_id
			OR v.org_id = c.org_id
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.project_id IS NOT NULL THEN 1
				WHEN v.client_id IS NOT NULL THEN 2
				WHEN v.org_id IS NOT NULL THEN 3
			END,
			v.name;

	ELSIF entity_type_param = 'client' THEN
		-- For clients, return client-level and organization-level vendors
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		JOIN public.client c ON c.client_id = entity_id_param
		WHERE (
			v.client_id = entity_id_param
			OR v.org_id = c.org_id
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.client_id IS NOT NULL THEN 1
				WHEN v.org_id IS NOT NULL THEN 2
			END,
			v.name;

	ELSIF entity_type_param = 'organization' THEN
		-- For organizations, return organization-level, client-level, and project-level vendors within the organization
		RETURN QUERY
		SELECT
			v.vendor_id, v.name, v.description, v.vendor_type,
			v.contact_name, v.contact_email, v.contact_phone, v.is_active,
			CASE
				WHEN v.project_id IS NOT NULL THEN 'project'
				WHEN v.client_id IS NOT NULL THEN 'client'
				WHEN v.org_id IS NOT NULL THEN 'organization'
			END as access_level
		FROM public.vendor v
		LEFT JOIN public.client c ON v.client_id = c.client_id
		LEFT JOIN public.project p ON v.project_id = p.project_id
		LEFT JOIN public.client pc ON p.client_id = pc.client_id
		WHERE (
			-- Organization-level vendors
			v.org_id = entity_id_param
			-- Client-level vendors within this organization
			OR (v.client_id IS NOT NULL AND c.org_id = entity_id_param)
			-- Project-level vendors within this organization
			OR (v.project_id IS NOT NULL AND pc.org_id = entity_id_param)
		)
		AND v.is_active = true
		ORDER BY
			CASE
				WHEN v.org_id IS NOT NULL THEN 1
				WHEN v.client_id IS NOT NULL THEN 2
				WHEN v.project_id IS NOT NULL THEN 3
			END,
			v.name;
	END IF;
END;
$$;

ALTER FUNCTION "public"."get_accessible_vendors" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_accessible_vendors" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Returns vendors accessible to a user at a specific scope, following the hierarchical access model. Organization scope includes all vendors within the organization hierarchy (org, client, and project level). Client scope includes client and organization level vendors. Project scope includes project, client, and organization level vendors.';

-- Function to get vendor creation hierarchy for current user
CREATE OR REPLACE FUNCTION "public"."get_vendor_creation_hierarchy" () RETURNS TABLE (
	"entity_type" "text",
	"entity_id" "uuid",
	"entity_name" "text",
	"parent_entity_type" "text",
	"parent_entity_id" "uuid",
	"parent_entity_name" "text",
	"grandparent_entity_type" "text",
	"grandparent_entity_id" "uuid",
	"grandparent_entity_name" "text",
	"user_role" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	current_user_id uuid;
BEGIN
	-- Get the current user ID once
	current_user_id := auth.uid();
	-- Return organizations where user has admin role
	RETURN QUERY
	SELECT
		'organization'::text as entity_type,
		o.org_id as entity_id,
		o.name as entity_name,
		NULL::text as parent_entity_type,
		NULL::uuid as parent_entity_id,
		NULL::text as parent_entity_name,
		NULL::text as grandparent_entity_type,
		NULL::uuid as grandparent_entity_id,
		NULL::text as grandparent_entity_name,
		m.role::text as user_role
	FROM public.organization o
	JOIN public.membership m ON m.entity_id = o.org_id
	WHERE m.user_id = current_user_id
		AND m.entity_type = 'organization'
		AND m.role >= 'admin'
	ORDER BY o.name;

	-- Return clients where user has admin role OR belongs to organization where user is admin
	RETURN QUERY
	SELECT
		'client'::text as entity_type,
		c.client_id as entity_id,
		c.name as entity_name,
		'organization'::text as parent_entity_type,
		o.org_id as parent_entity_id,
		o.name as parent_entity_name,
		NULL::text as grandparent_entity_type,
		NULL::uuid as grandparent_entity_id,
		NULL::text as grandparent_entity_name,
		COALESCE(cm.role, om.role)::text as user_role
	FROM public.client c
	JOIN public.organization o ON o.org_id = c.org_id
	LEFT JOIN public.membership cm ON cm.entity_id = c.client_id
		AND cm.user_id = current_user_id
		AND cm.entity_type = 'client'
		AND cm.role >= 'admin'
	LEFT JOIN public.membership om ON om.entity_id = o.org_id
		AND om.user_id = current_user_id
		AND om.entity_type = 'organization'
		AND om.role >= 'admin'
	WHERE (cm.user_id IS NOT NULL OR om.user_id IS NOT NULL)
	ORDER BY o.name, c.name;

	-- Return projects where user has editor+ role OR belongs to client/organization where user has appropriate access
	RETURN QUERY
	SELECT
		'project'::text as entity_type,
		p.project_id as entity_id,
		p.name as entity_name,
		'client'::text as parent_entity_type,
		c.client_id as parent_entity_id,
		c.name as parent_entity_name,
		'organization'::text as grandparent_entity_type,
		o.org_id as grandparent_entity_id,
		o.name as grandparent_entity_name,
		COALESCE(pm.role, cm.role, om.role)::text as user_role
	FROM public.project p
	JOIN public.client c ON c.client_id = p.client_id
	JOIN public.organization o ON o.org_id = c.org_id
	LEFT JOIN public.membership pm ON pm.entity_id = p.project_id
		AND pm.user_id = current_user_id
		AND pm.entity_type = 'project'
		AND pm.role >= 'editor'
	LEFT JOIN public.membership cm ON cm.entity_id = c.client_id
		AND cm.user_id = current_user_id
		AND cm.entity_type = 'client'
		AND cm.role >= 'admin'
	LEFT JOIN public.membership om ON om.entity_id = o.org_id
		AND om.user_id = current_user_id
		AND om.entity_type = 'organization'
		AND om.role >= 'admin'
	WHERE (pm.user_id IS NOT NULL OR cm.user_id IS NOT NULL OR om.user_id IS NOT NULL)
	ORDER BY o.name, c.name, p.name;
END;
$$;

ALTER FUNCTION "public"."get_vendor_creation_hierarchy" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_vendor_creation_hierarchy" () IS 'Returns hierarchical data for vendor creation form, including organizations, clients, and projects accessible to the current user with appropriate permissions';

-- Function to validate vendor scope consistency
CREATE OR REPLACE FUNCTION "public"."validate_vendor_scope" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	-- Ensure exactly one scope is set (this is also enforced by check constraint)
	IF (
		(NEW.org_id IS NOT NULL)::integer +
		(NEW.client_id IS NOT NULL)::integer +
		(NEW.project_id IS NOT NULL)::integer
	) != 1 THEN
		RAISE EXCEPTION 'Vendor must be associated with exactly one scope (organization, client, or project)';
	END IF;

	-- Validate hierarchy consistency
	IF NEW.client_id IS NOT NULL THEN
		-- Ensure client exists
		IF NOT EXISTS (SELECT 1 FROM public.client WHERE client_id = NEW.client_id) THEN
			RAISE EXCEPTION 'Client does not exist';
		END IF;
	END IF;

	IF NEW.project_id IS NOT NULL THEN
		-- Ensure project exists
		IF NOT EXISTS (SELECT 1 FROM public.project WHERE project_id = NEW.project_id) THEN
			RAISE EXCEPTION 'Project does not exist';
		END IF;
	END IF;

	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."validate_vendor_scope" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."validate_vendor_scope" () IS 'Validates vendor scope consistency and hierarchy relationships';

-- Validation trigger for vendor table
CREATE OR REPLACE TRIGGER "validate_vendor_scope_trigger" BEFORE INSERT
OR
UPDATE ON "public"."vendor" FOR EACH ROW
EXECUTE FUNCTION "public"."validate_vendor_scope" ();
